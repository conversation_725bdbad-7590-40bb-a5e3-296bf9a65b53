"""
Centralized AWS client initialization and management with lazy loading.
"""

import os
from functools import lru_cache
from typing import Optional

import boto3
from botocore.config import Config
from botocore.exceptions import NoCredentialsError

from ..config import settings
from ..logging_config import get_logger

logger = get_logger(__name__)


class AWSClients:
    """Singleton for AWS client management with lazy initialization."""

    _instance: Optional['AWSClients'] = None
    _s3_client: Optional[object] = None
    _dynamodb_resource: Optional[object] = None
    _sqs_client: Optional[object] = None
    _ses_client: Optional[object] = None
    _jobs_table: Optional[object] = None
    _preferences_table: Optional[object] = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    @staticmethod
    def get_boto_config():
        """Get boto3 configuration with timeouts."""
        return Config(
            region_name=settings.aws_region,
            read_timeout=30,
            connect_timeout=10,
            retries={
                'max_attempts': 3,
                'mode': 'standard'
            }
        )

    @property
    def s3(self):
        """Get S3 client with lazy initialization."""
        if self._s3_client is None:
            logger.info("Initializing S3 client with timeout configuration")
            config = self.get_boto_config()
            # Enable S3 acceleration if configured
            if settings.s3_transfer_acceleration:
                config = Config(
                    region_name=settings.aws_region,
                    read_timeout=30,
                    connect_timeout=10,
                    retries={
                        'max_attempts': 3,
                        'mode': 'standard'
                    },
                    s3={'use_accelerate_endpoint': True}
                )
            self._s3_client = boto3.client("s3", config=config)
        return self._s3_client

    @property
    def dynamodb(self):
        """Get DynamoDB resource with lazy initialization."""
        if self._dynamodb_resource is None:
            logger.info("Initializing DynamoDB resource with timeout configuration")
            self._dynamodb_resource = boto3.resource("dynamodb", config=self.get_boto_config())
        return self._dynamodb_resource

    @property
    def sqs(self):
        """Get SQS client with lazy initialization."""
        if self._sqs_client is None:
            logger.info("Initializing SQS client with timeout configuration")
            self._sqs_client = boto3.client("sqs", config=self.get_boto_config())
        return self._sqs_client

    @property
    def ses(self):
        """Get SES client with lazy initialization."""
        if self._ses_client is None:
            logger.info("Initializing SES client with timeout configuration")
            self._ses_client = boto3.client("ses", config=self.get_boto_config())
        return self._ses_client

    @property
    def jobs_table(self):
        """Get jobs table with lazy initialization."""
        if self._jobs_table is None:
            jobs_table_name = os.getenv("JOBS_TABLE_NAME", "biormika-analysis-jobs")
            if jobs_table_name:
                logger.info(f"Initializing jobs table: {jobs_table_name}")
                self._jobs_table = self.dynamodb.Table(jobs_table_name)
        return self._jobs_table

    @property
    def preferences_table(self):
        """Get preferences table with lazy initialization."""
        if self._preferences_table is None:
            preferences_table_name = os.getenv("PREFERENCES_TABLE_NAME", "biormika-user-preferences")
            if preferences_table_name:
                logger.info(f"Initializing preferences table: {preferences_table_name}")
                self._preferences_table = self.dynamodb.Table(preferences_table_name)
        return self._preferences_table

    @property
    def sqs_queue_url(self):
        """Get SQS queue URL."""
        return os.getenv("SQS_QUEUE_URL", "")

    @property
    def s3_bucket_name(self):
        """Get S3 bucket name."""
        return settings.s3_bucket_name

    @property
    def is_configured(self) -> bool:
        """Check if AWS services are properly configured."""
        return bool(
            self.s3_bucket_name and
            os.getenv("JOBS_TABLE_NAME") and
            os.getenv("PREFERENCES_TABLE_NAME")
        )


@lru_cache(maxsize=1)
def get_aws_clients() -> AWSClients:
    """Get singleton AWS clients instance."""
    return AWSClients()
